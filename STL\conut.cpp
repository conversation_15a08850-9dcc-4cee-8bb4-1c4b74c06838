#include<iostream>
using namespace std;
#include<vector>
#include<algorithm>
class person
{

public:
    person(string name,int age){
        this->m_name = name;
        this->m_age = age;
    }
    int m_age;
    string m_name;
    bool operator==(const person&p){
        if(this->m_age==p.m_age&&this->m_name==p.m_name){
            return true;
        }
        else{
            return false;
        }
    }

};


void test01(){
    vector<person>v;
    person p1("tom",18);
    person p2("sam",20);
    person p3("dean",22);
    person p4("allen",22);
    v.push_back(p1);
    v.push_back(p2);
    v.push_back(p3);
    v.push_back(p4);
    person p("dean",22);
    int count1 = count(v.begin(),v.end(),p);
    cout<<"num = "<<count1<<endl;
    
}
int main(){
    test01();
    system("pause");
}