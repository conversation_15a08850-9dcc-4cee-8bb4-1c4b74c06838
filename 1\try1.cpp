//???????
#include <iostream>

class Person{
public:
    int m_A;
    int m_B;
public:
    Person(){
        m_A = 0;
        m_B = 0;
    }
    Person(int a, int b){
        this->m_A = a;
        this->m_B = b;
    }

    Person operator+ (const Person&p){
        Person temp;
        temp.m_A = this->m_A + p.m_A;
        temp.m_B = this->m_B + p.m_B;
        return temp;
    }
};

Person operator+(const Person&p1,const Person&p2){
    Person temp;
    temp.m_A = p1.m_A + p2.m_A;
    temp.m_B = p1.m_B + p2.m_B;
    return temp;
}
Person operator+(const Person&p1,const int val){
    Person temp;
    temp.m_A = p1.m_A + val;
    temp.m_B = p1.m_B + val;
    return temp;
}


void test01(){
    Person a(23,24);
    Person b(1,1);
    Person c = a + 10 + 1;
    std::cout<<c.m_A<<"\n";
    std::cout<<c.m_B<<"\n";

    Person d = a + b + c;
    std::cout<<d.m_A<<"\n";
    std::cout<<d.m_B<<"\n";
}

int main(){
    test01();
    system("pause");
}