#include <iostream>
#include <string>
#include <algorithm>
#include <vector>
using namespace std;

bool canFormFromYesOrSey(string s) {
    transform(s.begin(), s.end(), s.begin(), ::tolower);
    int n = s.length();
    vector<bool> dp(n + 1, false);
    
    dp[0] = true; 

    for (int i = 3; i <= n; i++) {
        if (i >= 3 && dp[i - 3] && s.substr(i - 3, 3) == "yes") {
            dp[i] = true;
        }
        if (i >= 3 && dp[i - 3] && s.substr(i - 3, 3) == "sey") {
            dp[i] = true;
        }
    }
    return dp[n];
}

int main() {
    int T;
    cin >> T;

    while (T--) {
        int n;
        string s;
        cin >> n >> s;
        if (canFormFromYesOrSey(s)) {
            cout << "Yes" << endl;
        } else {
            cout << "No" << endl;
        }
    }

    return 0;
}