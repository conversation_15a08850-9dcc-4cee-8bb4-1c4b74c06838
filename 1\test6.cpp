#include <iostream>
using namespace std;

class Person{
    public:
        Person(){
            m_A = 0;
            m_B = 0;
        }

        void ShowPerson()const{
            // this->m_A = 20;
            this->m_B  = 10;
        }

        int m_A;
        mutable int m_B;

        void test(){
            cout<<"test 's diaoyong"<<endl;
        }


};
void test01(){
    const Person p;
    cout<<p.m_A<<endl;
    //p.m_A = 100;
    p.m_B = 200;
    p.ShowPerson();
    //p.test();

}
int main(){
    test01();
    system("pause");
    return 0 ;
}