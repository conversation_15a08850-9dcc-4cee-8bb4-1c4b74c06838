#include "txl.h"

void showPerson(Addressbooks *a){
    if(a->size == 0){
        std::cout << "当前通讯录为空" << std::endl;
    }
    else{
        for(int i = 0; i < a->size; i++){
            std::cout << "\n"
                      << "第 " << i+1 << " 个联系人信息：\n"
                      << "姓名：" << a->personArray[i].name << "\n"
                      << "年龄：" << a->personArray[i].age << "\n"
                      << "性别：" << (a->personArray[i].gender == 1 ? "男" : "女") << "\n"
                      << "电话：" << a->personArray[i].phone << "\n"
                      << "地址：" << a->personArray[i].address << "\n"
                      << "------------------------" << std::endl;
        }
    }
    system("pause");
    system("cls");
}