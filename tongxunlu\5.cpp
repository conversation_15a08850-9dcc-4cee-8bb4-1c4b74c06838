#include "txl.h"

void modifyPerson(Addressbooks *a){
    std::cout<<"请输入您要修改的联系人姓名："<<std::endl;
    std::string name;
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    std::getline(std::cin, name);

    int ret = isExist(a,name);
    if(ret==-1){
        std::cout<<"查无此人"<<std::endl;
    }
    
    else{
        std::cout << "请输入姓名：";
        std::getline(std::cin, name);
        
        int age=0;
        while(true){
            std::cout<<"请输入年龄："<<std::endl;
            if(std::cin >> age && age > 0){
                break;
            }
            std::cout << "年龄输入无效，请输入正整数" << std::endl;
            std::cin.clear();
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
        }
        std::cin.ignore();
        std::cout << "请输入性别：" << std::endl;
        std::cout << "1---男" << std::endl;
        std::cout << "2---女" << std::endl;
        int gender=0;
        while(true){
            std::cin >> gender;
            if(gender == 1 || gender == 2){
                a->personArray[ret].gender = gender;
                break;
            }
            std::cout << "输入错误，请重新输入" << std::endl;
            std::cin.clear();
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
        }
        std::cout<<"请输入电话："<<std::endl;
        std::string phone="";
        std::cin.ignore();
        std::getline(std::cin, phone);
        
        std::cout<<"请输入地址："<<std::endl;
        std::string address="";
        std::getline(std::cin, address);
        a->personArray[ret].name = name;
        a->personArray[ret].age = age;
        a->personArray[ret].gender = gender;
        a->personArray[ret].phone = phone;
        a->personArray[ret].address = address;
        std::cout<<"修改成功"<<std::endl;
    }
    system("pause");
    system("cls");
    
}
