#include<iostream>
using namespace std;
class Cube
{
public:
	void set_l(int l){
		m_L = l;
	}
	int get_l(){
		return m_L;
	}	

	void set_w(int w){
		m_W = w;
	}
	int get_w(){
		return m_W;
	}

	void set_h(int h){
		m_H = h;
	}
	int get_h(){
		return m_H;
	}	
    
	int calculateS(){
		return 2*m_L*m_W+2*m_L*m_H+2*m_H*m_W;
	}
	int calculateV(){
		return m_L*m_H*m_W;
	}
	bool isSameByClass(Cube &c){
		if(m_L==c.get_l()&&m_W==c.get_w()&&m_H==c.get_h()){
			return true;
		}
		return false;
	};

private:
	
	int m_L;
	int m_W;
	int m_H;
};

bool isSame(Cube &a,Cube &b){
	if(a.get_l()==b.get_l()&&a.get_w()==b.get_w()&&a.get_h()==b.get_h()){
		return true;
	}
	return false;
}

int main(){
	Cube c1;
	c1.set_l(10);
	c1.set_h(10);
	c1.set_w(10);
	Cube c2;
	c2.set_l(10);
	c2.set_h(10);
	c2.set_w(10);
	
	bool judge = isSame(c1,c2);

	if(judge){
		cout<<"global : c1 and c2 are the same"<<endl;
	}else{
		cout<<"global : c1 and c2 are not the same"<<endl;
	}

	judge = c1.isSameByClass(c2);//已知的对象调用成员函数，传入未知

	if(judge){
		cout<<"class : c1 and c2 are the same"<<endl;
	}else{
		cout<<"class : c1 and c2 are not the same"<<endl;
	}

	system("pause");
	return 0;
}
