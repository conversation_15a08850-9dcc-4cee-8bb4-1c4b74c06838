#include<iostream>
using namespace std;
struct student{
    string sname;
    int score;
};
struct teacher{
    string tname;
    student sarr[5];
};
void allocate_teacher(teacher tarr[],int len){
    string nameSeed="ABCDE";
    for(int i=0;i<len;i++){
        tarr[i].tname="Teacher_";
        tarr[i].tname+=nameSeed[i];
        for(int j=0;j<5;j++){
            tarr[i].sarr[j].sname="Student_";
            tarr[i].sarr[j].sname+=nameSeed[j];
            int random=rand()%60+40;
            tarr[i].sarr[j].score=random;
        }
    }
}
void printInfo(teacher tarr[],int len){
    for(int i=0;i<len;i++){
        cout<<"老师姓名："<<tarr[i].tname<<endl;
        for(int j=0;j<5;j++){
            cout<<"\t学生姓名："<<tarr[i].sarr[j].sname<<"\t"
            <<"学生分数："<<tarr[i].sarr[j].score<<endl;
        }
        cout<<endl;
    }
}
int main(){
    //随机数种子
    srand(time (NULL));
    teacher tarr[3];
    int len=sizeof(tarr)/sizeof(tarr[0]);
    allocate_teacher(tarr,len);
    printInfo(tarr,len);
    return 0;
}