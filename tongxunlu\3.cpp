#include "txl.h"

int isExist(Addressbooks *a,std::string name){
    for(int i=0;i<a->size;i++){
        if(a->personArray[i].name==name){
            return i;
        }
    }
    return -1;
}

void deletePerson(Addressbooks *a){
    std::string name;
    std::cout<<"请输入您要删除的联系人姓名："<<std::endl;
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    std::getline(std::cin, name);
    int ret = isExist(a,name);
    if(ret==-1){
        std::cout<<"查无此人"<<std::endl;
    }
    else{
        for(int i = ret; i < a->size - 1; i++){
            a->personArray[i] = a->personArray[i+1];
        }
        a->size--;
        std::cout<<"删除成功"<<std::endl;
    }
    system("pause");
    system("cls");
}