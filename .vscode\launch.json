{"version": "0.2.0", "configurations": [{"name": "(gdb) 启动", "type": "cppdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [{"name": "PYTHONIOENCODING", "value": "utf8"}, {"name": "LANG", "value": "zh_CN.GBK"}, {"name": "PYTHONLEGACYWINDOWSSTDIO", "value": "utf-8"}], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "C:\\MinGW\\bin\\gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}, {"description": "设置终端编码为GBK", "text": "set charset GBK", "ignoreFailures": true}], "preLaunchTask": "C/C++: g++.exe build active file"}]}