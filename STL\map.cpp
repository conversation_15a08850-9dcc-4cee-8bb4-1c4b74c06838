#include<iostream>
using namespace std;
#include<map>
class MyCompare{
public:
    bool operator()(int v1,int v2){
        return v1>v2;
    }
};
void printMap(map<int,int,MyCompare>&a){
    for(auto it=a.begin();it!=a.end();it++){
        cout<<"k: "<<it->first<<" v: "<<it->second<<endl;
    }

}
void test01(){
    map<int,int,MyCompare>m;
    m.insert(make_pair(1,10));
    m.insert(make_pair(4,40));
    m.insert(make_pair(2,20));
    m.insert(make_pair(3,30));
    //m.insert(make_pair(3,40));
    printMap(m);
}
int main(){
    test01();
    system("pause");
}