#include<iostream>

struct hero{
    std::string name;
    int age;
    std::string sex;
};

void swap(int &a,int &b){
    int temp=a;
    a=b;
    b=temp;
}

void swapString(std::string &a,std::string &b){
    std::string temp=a;
    a=b;
    b=temp;
}

void bubbleSort( hero arr[],int len){
    for(int i =0;i<len-1;i++){
        for(int j=0;j<len-i-1;j++){
            if(arr[j].age>arr[j+1].age){
                swap(arr[j].age,arr[j+1].age);
                swapString(arr[j].name,arr[j+1].name);
                swapString(arr[j].sex,arr[j+1].sex);
            }
        }
    }
}
void printInfo(const hero arr[],int len){
    for(int i=0;i<len;i++){
        std::cout<<"姓名："<<arr[i].name<<"\t"<<"年龄："<<arr[i].age<<"\t"<<"性别："<<arr[i].sex<<"\n";
    }
}
int main(){
    hero harr[5]={
        {"刘备",23,"男"},
        {"关羽",22,"男"},
        {"张飞",20,"男"},
        {"赵云",21,"男"},
        {"貂蝉",19,"女"}
    };
    int len = sizeof(harr)/sizeof(harr[0]);
    bubbleSort(harr,len);
    printInfo(harr,len);
    return 0;
}