#include "txl.h"

void findPerson(Addressbooks *a){
    std::cout<<"请输入您要查找的联系人姓名："<<std::endl;
    std::string name;
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    std::getline(std::cin, name);
    int ret = isExist(a,name);
    if(ret==-1){
        std::cout<<"查无此人"<<std::endl;
    }
    else{
        std::cout<<"姓名："<<a->personArray[ret].name<<std::endl;
        std::cout<<"年龄："<<a->personArray[ret].age<<std::endl;
        std::cout<<"性别："<<(a->personArray[ret].gender==1?"男":"女")<<std::endl;
        std::cout<<"电话："<<a->personArray[ret].phone<<std::endl;
        std::cout<<"地址："<<a->personArray[ret].address<<std::endl;
    }
    system("pause");
    system("cls");
}
