#include <iostream>
#include <vector>
#include <queue>
#include <climits>
using namespace std;

typedef long long ll;
typedef pair<ll, int> P;

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int n, m, c;
    cin >> n >> m >> c;

    vector<int> colors(n + 1); // 节点颜色，1-based
    for (int i = 1; i <= n; ++i) {
        cin >> colors[i];
    }

    vector<int> k(c + 1); // 每种颜色的攻击距离，1-based
    for (int i = 1; i <= c; ++i) {
        cin >> k[i];
    }

    vector<vector<pair<int, int>>> adj(n + 1); // 邻接表: {目标节点, 边权}

    for (int i = 0; i < m; ++i) {
        int u, v, d;
        cin >> u >> v >> d;
        adj[u].emplace_back(v, d);
    }

    vector<bool> is_safe(n + 1, true); // 标记每个节点是否安全

    // 对每个节点，检查是否能被同色节点攻击到
    for (int target = 1; target <= n; ++target) {
        int target_color = colors[target];
        int attack_range = k[target_color];

        // 使用Dijkstra算法计算从所有同色节点到target的最短距离
        priority_queue<P, vector<P>, greater<P>> pq;
        vector<ll> dist(n + 1, LLONG_MAX);

        // 将所有同色节点作为起点
        for (int i = 1; i <= n; ++i) {
            if (colors[i] == target_color && i != target) {
                dist[i] = 0;
                pq.push({0, i});
            }
        }

        while (!pq.empty()) {
            auto [current_dist, u] = pq.top();
            pq.pop();

            if (current_dist > dist[u]) continue;

            for (const auto& [v, d] : adj[u]) {
                if (dist[v] > current_dist + d) {
                    dist[v] = current_dist + d;
                    pq.push({dist[v], v});
                }
            }
        }

        // 检查是否有同色节点能在攻击距离内到达target
        if (dist[target] <= attack_range) {
            is_safe[target] = false;
        }
    }

    int safe_count = 0;
    for (int i = 1; i <= n; ++i) {
        if (is_safe[i]) {
            safe_count++;
        }
    }

    cout << safe_count << endl;

    return 0;
}