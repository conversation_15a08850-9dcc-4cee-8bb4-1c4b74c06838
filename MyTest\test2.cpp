#include <iostream>
#include <vector>
#include <queue>
#include <climits>
#include <unordered_map>
using namespace std;

typedef long long ll;
typedef pair<ll, int> P; 

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int n, m, c;
    cin >> n >> m >> c;

    vector<int> colors(n + 1); // 1-based
    for (int i = 1; i <= n; ++i) {
        cin >> colors[i];
    }

    vector<int> k(c + 1); // 1-based
    for (int i = 1; i <= c; ++i) {
        cin >> k[i];
    }

    vector<vector<pair<int, int>>> adj(n + 1); // original graph: {v, d}

    for (int i = 0; i < m; ++i) {
        int u, v, d;
        cin >> u >> v >> d;
        adj[u].emplace_back(v, d);
    }

    unordered_map<int, vector<int>> color_to_nodes;
    for (int i = 1; i <= n; ++i) {
        color_to_nodes[colors[i]].push_back(i);
    }

    vector<bool> is_safe(n + 1, true);

    for (const auto& entry : color_to_nodes) {
        int color = entry.first;
        const vector<int>& nodes = entry.second;
        int max_k = k[color];

        for (int source : nodes) {
            priority_queue<P, vector<P>, greater<P>> pq;
            vector<ll> dist(n + 1, LLONG_MAX);
            dist[source] = 0;
            pq.push({0, source});

            while (!pq.empty()) {
                auto [current_dist, u] = pq.top();
                pq.pop();

                if (current_dist > dist[u]) continue;

                for (const auto& [v, d] : adj[u]) {
                    if (colors[v] != color) continue; // only same color
                    if (dist[v] > current_dist + d) {
                        dist[v] = current_dist + d;
                        pq.push({dist[v], v});
                    }
                }
            }

            for (int u : nodes) {
                if (u != source && dist[u] <= max_k) {
                    is_safe[u] = false;
                }
            }
        }
    }

    int safe_count = 0;
    for (int i = 1; i <= n; ++i) {
        if (is_safe[i]) {
            safe_count++;
        }
    }

    cout << safe_count << endl;

    return 0;
}