#include <iostream>
#include <vector>
#include <queue>
#include <climits>
#include <unordered_map>
using namespace std;

typedef long long ll;
typedef pair<ll, int> P;

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int n, m, c;
    cin >> n >> m >> c;

    vector<int> colors(n + 1); // 节点颜色，1-based
    for (int i = 1; i <= n; ++i) {
        cin >> colors[i];
    }

    vector<int> k(c + 1); // 每种颜色的攻击距离，1-based
    for (int i = 1; i <= c; ++i) {
        cin >> k[i];
    }

    vector<vector<pair<int, int>>> adj(n + 1); // 邻接表: {目标节点, 边权}

    for (int i = 0; i < m; ++i) {
        int u, v, d;
        cin >> u >> v >> d;
        adj[u].emplace_back(v, d);
    }

    // 按颜色分组节点
    unordered_map<int, vector<int>> color_nodes;
    for (int i = 1; i <= n; ++i) {
        color_nodes[colors[i]].push_back(i);
    }

    vector<bool> is_safe(n + 1, true); // 标记每个节点是否安全

    // 对每种颜色分别处理
    for (const auto& [color, nodes] : color_nodes) {
        if (nodes.size() <= 1) continue; // 只有一个节点的颜色，该节点必然安全

        int attack_range = k[color];

        // 对该颜色的每个节点作为起点，运行Dijkstra
        for (int source : nodes) {
            priority_queue<P, vector<P>, greater<P>> pq;
            vector<ll> dist(n + 1, LLONG_MAX);
            vector<bool> visited(n + 1, false);
            dist[source] = 0;
            pq.push({0, source});

            while (!pq.empty()) {
                auto [current_dist, u] = pq.top();
                pq.pop();

                if (visited[u]) continue;
                visited[u] = true;

                // 如果当前距离已经超过攻击范围，跳过
                if (current_dist > attack_range) continue;

                for (const auto& [v, d] : adj[u]) {
                    ll new_dist = current_dist + d;
                    if (!visited[v] && dist[v] > new_dist) {
                        dist[v] = new_dist;
                        pq.push({new_dist, v});
                    }
                }
            }

            // 检查该起点能攻击到哪些同色节点
            for (int target : nodes) {
                if (target != source && dist[target] <= attack_range) {
                    is_safe[target] = false;
                }
            }
        }
    }

    int safe_count = 0;
    for (int i = 1; i <= n; ++i) {
        if (is_safe[i]) {
            safe_count++;
        }
    }

    cout << safe_count << endl;

    return 0;
}