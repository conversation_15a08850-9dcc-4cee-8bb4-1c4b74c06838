//???????
#include <iostream>

class Person{

friend std::ostream& operator<<(std::ostream &cout,Person &p);

public:
    Person(){

    }
    Person(int a,int b){
        this->m_a = a;
        this->m_b = b;

    }

private:
    int m_a = 0;
    int m_b = 0;

};

std::ostream& operator<<(std::ostream &cout, Person &p){
    cout << "m_a:" << p.m_a << "   m_b:" << p.m_b << std::endl;
    return cout;
}

void test01(){
    Person p(10,20);
    std::cout<<p<<std::endl;
    
}

int main(){
    test01();
    system("pause");
}