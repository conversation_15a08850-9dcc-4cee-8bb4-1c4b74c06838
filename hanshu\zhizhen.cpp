#include<iostream>
using namespace std;
// int main() {

//     //1、指针的定义
//     int a = 10; //定义整型变量a

//     //指针定义语法： 数据类型 * 变量名 ;
//     int * p;

//     //指针变量赋值
//     p = &a; //指针指向变量a的地址
//     cout << &a << endl; //打印数据a的地址
//     cout << p << endl;  //打印指针变量p

//     //2、指针的使用
//     //通过*操作指针变量指向的内存
//     cout << "*p = " << *p << endl;

//     system("pause");

//     return 0;
// }
int main() {

    int a = 10;
    int b = 20;

    const int *p =&a;//const 后面是int，指针指向的int值不可修改，指针的指向可以修改
    p=&b;
    //*p=100;
    
    int *const p2=&b;//const 后面是p，指针的指向不可修改，指针指向的int值可以修改
    *p2=100;
    //p2=&a;

    system("pause");

    return 0;
}
