#include<iostream>

class Person
{
    friend void test01();
private:
    int *m_age;
public:
    Person(int age);
    ~Person();
    Person& operator=(Person&p);
};

Person::Person(int age)
{   
    m_age = new int(age);
}

Person::~Person()
{   
    if(m_age!=NULL){
        delete m_age;
        m_age = NULL;
    }
}

Person& Person::operator=(Person&p){
    if(m_age!=NULL){
        delete m_age;
        m_age = NULL;
    }
    m_age = new int (*p.m_age);
    return *this;
}
void test01(){
    Person p1(10);
    Person p2(20);
    Person p3(30);
    p3 =p2 =p1;
    std::cout<<"P1: "<<*p1.m_age<<std::endl;
    std::cout<<"P2: "<<*p2.m_age<<std::endl;
    std::cout<<"P3: "<<*p3.m_age<<std::endl;
}
int main(){
    test01();
    system("pause");
}