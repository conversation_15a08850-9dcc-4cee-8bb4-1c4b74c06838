#include <iostream>
#include <vector>
#include <sstream>
#include <string>
#include <algorithm>
#include <queue>
using namespace std;

// TreeNode definition
struct TreeNode {
    int val;
    TreeNode* left;
    TreeNode* right;
    TreeNode(int x) : val(x), left(NULL), right(NULL) {}
};

// Function to build the binary tree from the input array
TreeNode* buildTree(const vector<string>& nodes) {
    if (nodes.empty() || nodes[0] == "N") return nullptr;

    TreeNode* root = new TreeNode(stoi(nodes[0]));
    queue<TreeNode*> q;
    q.push(root);
    int i = 1;

    while (i < nodes.size()) {
        TreeNode* current = q.front();
        q.pop();

        if (nodes[i] != "N") {
            current->left = new TreeNode(stoi(nodes[i]));
            q.push(current->left);
        }
        i++;
        if (i < nodes.size() && nodes[i] != "N") {
            current->right = new TreeNode(stoi(nodes[i]));
            q.push(current->right);
        }
        i++;
    }

    return root;
}

// Function to calculate the minimum number of base stations
int minBaseStations(TreeNode* root, int& covered) {
    if (!root) {
        covered = 1; // Null nodes are considered covered
        return 0;
    }

    int leftCovered, rightCovered;
    int leftStations = minBaseStations(root->left, leftCovered);
    int rightStations = minBaseStations(root->right, rightCovered);

    if (leftCovered == 0 || rightCovered == 0) {
        covered = 2; // This node needs a base station
        return leftStations + rightStations + 1;
    }

    if (leftCovered == 2 || rightCovered == 2) {
        covered = 1; // This node is covered by a base station
        return leftStations + rightStations;
    }

    covered = 0; // This node is not covered yet
    return leftStations + rightStations;
}

int minBaseStations(TreeNode* root) {
    int covered = 0;
    int result = minBaseStations(root, covered);
    if (covered == 0) result++; // If the root is not covered, add one base station
    return result;
}

int main() {
    string input;
    getline(cin, input);

    // Parse the input string
    vector<string> nodes;
    stringstream ss(input);
    string token;
    while (ss >> token) {
        nodes.push_back(token);
    }

    // Build the tree
    TreeNode* root = buildTree(nodes);

    // Calculate and output the minimum number of base stations
    cout << minBaseStations(root) << endl;

    return 0;
}