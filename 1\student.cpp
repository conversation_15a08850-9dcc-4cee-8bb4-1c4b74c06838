#include <iostream>
using namespace std;
class student
{
public:

	string name;
	int number;
    
public:
	void show()
	{
		cout<<"name is "<<name<<endl;
		cout<<"number is "<<number<<endl;
	}
    void setname(string n){
        name=n;
    }
    void setnumber(int n){
        number=n;
    }
};
int main(){
	student s1,s2;
	s1.setname("Dean");
	s1.setnumber(616);
	s2.setname("Sam");
	s2.setnumber(1212);
	s1.show();
	s2.show();
    system("pause");
    return 0;
}
