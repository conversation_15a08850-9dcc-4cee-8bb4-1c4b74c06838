#include<iostream>
#include<vector>
#include<algorithm>
using namespace std;
class greater1{
public:
    bool operator()(int val){
        return val>1;
    }
};
void myPrint(int val){
    cout<<val<<" ";

}
void test01(){
    vector<int>v;
    v.push_back(1);
    v.push_back(2);
    v.push_back(1);
    v.push_back(2);
    v.push_back(1);
    v.push_back(2);
    v.push_back(1);
    v.push_back(2);
    int conuntv = count_if(v.begin(),v.end(),greater1());
    // for_each(v.begin(),v.end(),myPrint);
    cout<<conuntv<<endl;
}
class Person{
public:
    string m_name;
    int m_age;
    Person(string name,int age){
        this->m_age = age;
        this->m_name = name;
    }
};
class AgeGreater18{
public:
    bool operator()(const Person & p)const{
            return p.m_age>18;
        }
};

void test02(){
    vector<Person>v;
    Person p1 ("biy",18);
    Person p2 ("b213",19);
    Person p3 ("biy",20);
    Person p4 ("b213",17);
    v.push_back(p1);
    v.push_back(p2);
    v.push_back(p3);
    v.push_back(p4);
    int countp = count_if(v.begin(),v.end(),AgeGreater18());
    cout<<countp<<endl;
}
int main(){
    test02();
    system("pause");
}