#include <iostream>
class MyInteger{
    friend std::ostream& operator<<(std::ostream& cout, const MyInteger& myint);
public:
    MyInteger(){
        m_NUM = 0;
    }
    //qianzhi ++
    MyInteger& operator++(){
        m_NUM++;
        return *this;
    }
    //houzhi ++
    MyInteger operator++(int){
        MyInteger temp = *this;
        m_NUM++;
        return temp;
    }

private:
    int m_NUM;
};

std::ostream& operator<<(std::ostream& cout, const MyInteger& myint){
    cout << myint.m_NUM;
    return cout;
}

void test01(){
    MyInteger myint;
    std::cout << myint <<std::endl;
    std::cout << ++myint <<std::endl;
    std::cout << myint++ <<std::endl;
    std::cout << myint <<std::endl;
}

int main (){
    test01();
    system("pause");
}