#include <iostream>
using namespace std;

class Animal{
public:
    Animal(){
        cout<<"Animal's constructor"<<endl;
    }
    virtual ~Animal() = 0;
    virtual void speak()=0;
};
Animal::~Animal(){
    cout<<"Animal's destructor"<<endl;
}
class Cat:public Animal
{
public:
    Cat(string name){
        cout<<"Cat's constructor"<<endl;
        m_Name = new string(name);
    }
    ~Cat(){
        cout<<"Cat's destructor"<<endl;
        if(this->m_Name!=NULL){
            delete m_Name;
            m_Name = NULL;
        }
    }
    virtual void speak(){
        cout<<*m_Name<<" "<<"Cat is speaking"<<endl;
    }
public:
    string *m_Name;
};
void test01(){
    Animal *animal = new Cat("Tom");
    animal->speak();
    delete animal;
}
int main(){
    test01();
    system("pause");
}