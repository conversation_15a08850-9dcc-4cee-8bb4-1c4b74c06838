#include "txl.h"

void addPerson(Addressbooks *a){
    if(a->size == MAX){
        std::cout << "通讯录已满，无法添加" << std::endl;
        return;
    }
    else{
        // 姓名
        std::string name="";
        std::cout << "请输入姓名：";
        std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
        std::getline(std::cin, name);
        a->personArray[a->size].name = name;

        // 年龄
        int age=0;
        while(true){
            std::cout << "请输入年龄：";
            if(std::cin >> age && age > 0){
                a->personArray[a->size].age = age;
                break;
            }
            std::cout << "年龄输入无效，请输入正整数" << std::endl;
            std::cin.clear();
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
        }

        // 性别
        std::cout << "请输入性别：" << std::endl;
        std::cout << "1---男" << std::endl;
        std::cout << "2---女" << std::endl;
        int gender=0;
        while(true){
            std::cin >> gender;
            if(gender == 1 || gender == 2){
                a->personArray[a->size].gender = gender;
                break;
            }
            std::cout << "输入错误，请重新输入" << std::endl;
            std::cin.clear();
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
        }

        // 电话
        std::string phone="";
        std::cout << "请输入电话：";
        std::cin >> phone;
        a->personArray[a->size].phone = phone;

        // 地址
        std::string address="";
        std::cout << "请输入地址：";
        std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
        std::getline(std::cin, address);
        a->personArray[a->size].address = address;

        a->size++;
        std::cout << "添加成功" << std::endl;
        system("pause");
        system("cls");
    }
}