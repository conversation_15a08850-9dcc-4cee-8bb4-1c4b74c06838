#ifndef TXL_H
#define TXL_H

#include <iostream>
#include <string>
#include <limits>
#include <windows.h>
#define MAX 1000

struct Person {
    std::string name;
    int age;
    int gender; // 1 男 2 女
    std::string phone;
    std::string address;
};

struct Addressbooks {
    Person personArray[MAX];
    int size;
};

// 函数声明
void initConsole();
void showmenu();
void addPerson(Addressbooks *a);
void showPerson(Addressbooks *a);
int isExist(Addressbooks *a,std::string name);
void deletePerson(Addressbooks *a);
void findPerson(Addressbooks *a);
void modifyPerson(Addressbooks *a);
void clearPerson(Addressbooks *a);

#endif 