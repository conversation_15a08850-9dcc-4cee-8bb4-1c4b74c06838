#include<iostream>
class MyInter{
friend std::ostream& operator<<(std::ostream&cout,MyInter m);    
public:
    MyInter(){
        m_am = 0;
    }    
    //qianzhi --
    MyInter& operator--(){
        m_am--;
        return *this;
    }
    //houzhi --
    MyInter operator--(int){
        MyInter temp = *this;
        m_am--;
        return temp;
    }
private:
    int m_am;
};

std::ostream& operator<<(std::ostream&cout,MyInter m){
    std::cout<<m.m_am;
    return cout;
}
void test01(){
    MyInter myint;
    std::cout << myint <<std::endl;
    std::cout << --myint <<std::endl;
    std::cout << myint-- <<std::endl;
    std::cout << myint <<std::endl;
}

int main(){
    test01();
    system("pause");
}