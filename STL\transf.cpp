#include<iostream>
using namespace std;
#include <vector>
#include <algorithm>
#include<functional>
class myPrint{
public:
    void operator()(int val){
        cout<<val<<" ";
    }
};
// class mygreater{
// public:
//     int operator()(int val1,int val2){
//         return val1>val2;
//     }
// };
class trans{
public:
    int operator()(int val){
        return val;
    }
};
void test01(){
    vector<int>v;
    v.push_back(10);
    v.push_back(20);
    v.push_back(30);
    v.push_back(30);
    vector<int>v2;
    v2.resize(v.size());
    for_each(v.begin(),v.end(),myPrint());
    transform(v.begin(),v.end(),v2.begin(),trans());
    for_each(v2.begin(),v2.end(),myPrint());
    

}
int main(){
    test01();
    system("pause");
}