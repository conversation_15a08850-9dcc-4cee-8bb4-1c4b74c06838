#define NOMINMAX
#include "txl.h"

#define MAX 1000

void initConsole() {
    // UTF-8编码设置
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 本地化设置
    setlocale(LC_ALL, "zh_CN.UTF-8");
    
    // SimSun字体设置
    CONSOLE_FONT_INFOEX cfi;
    cfi.cbSize = sizeof(cfi);
    cfi.nFont = 0;
    cfi.dwFontSize.X = 0;
    cfi.dwFontSize.Y = 20;
    cfi.FontFamily = FF_DONTCARE;
    cfi.FontWeight = FW_NORMAL;
    
    // 简体中文系统支持的字体
    wcscpy(cfi.FaceName, L"SimSun");
    SetCurrentConsoleFontEx(
        GetStdHandle(STD_OUTPUT_HANDLE), 
        FALSE, 
        &cfi);
}

void showmenu(){
    std::cout<<"************************"<<std::endl;
    std::cout<<"***** 1.添加联系人 *****"<<std::endl;
    std::cout<<"***** 2.显示联系人 *****"<<std::endl;
    std::cout<<"***** 3.删除联系人 *****"<<std::endl;
    std::cout<<"***** 4.查找联系人 *****"<<std::endl;
    std::cout<<"***** 5.修改联系人 *****"<<std::endl;
    std::cout<<"***** 6.清空联系人 *****"<<std::endl;
    std::cout<<"***** 0.退出通讯录 *****"<<std::endl;
    std::cout<<"************************"<<std::endl;
}

int main(){
    initConsole();
    
    int select = 0;
    Addressbooks a;
    a.size = 0;
    
    while(true){
        showmenu();

        std::cout << "请输入选项数字：";
        
        // 必须保留的输入验证
        if(!(std::cin >> select)){
            std::cin.clear();
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
            std::cout << "输入无效，请输入数字" << std::endl;
            system("pause");
            system("cls");
            continue;
        }

        switch(select){
            case 1:
                addPerson(&a);
                break;
            case 2:
                showPerson(&a);
                break;  
            case 3:
                deletePerson(&a);
                break;
            case 4:
                findPerson(&a);
                break;
            case 5:
                modifyPerson(&a);
                break;
            case 6:
                clearPerson(&a);
                break;
            case 0:
                std::cout << "欢迎下次使用，退出通讯录" << std::endl;
                system("pause");
                return 0;
            default:
                std::cout << "输入错误，请重新输入" << std::endl;
                system("pause");
                system("cls");
                break;
        }
    }
    system("pause");
    return 0;
}